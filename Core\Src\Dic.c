/*~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Include headfile
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
#include "Dic.h"
#include "canopen.h"
#include "delay.h"
#include "modbus.h"
#include "string.h"
#include "mcpwm.h"
/***************************************************/

/*---------------------------------------------------------------------
                         Global define
-----------------------------------------------------------------------*/
extern u8 view_eerom[10];

u32 OD_Num=0x12345678;
	  
u8 eerom_pbuf[10];

DICT_OBJECT Access_OBJ;

u8 Eerom_Buffer[50][8];

u8 Write_Access=1;

u8 VCP_RX_Buffer[100];
u8 VCP_TX_Buffer[100]={0x01,0x02,0x03,0x05,0x08,0x06,0x07,0x08,0x09,0x0a};
u16 VCP_RX_Len=0;

const DICT_OBJECT OD_attribute[]=
{
/*
...Index..subindex
...............Data_type
...................OD_pointer..............................Max.........Min.........Flags...............................*Fp_R...........*Fp_W...
*/		  
////...IO function group

	// Device Configuration Group (0x1xxx)
	{0x1001, 0x00, 1, (u8*)&Error_register,     0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Error register
	{0x1010, 0x00, 1, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Store parameters
	{0x1010, 0x01, 4, (u8*)&store_parameter,    0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Store sub-index 1
	
	// Manufacturer Specific Group (0x2xxx)
	{0x2000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x2300, 0x00, 4, (u8*)&Error_State.all,    0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Error state
	{0x2301, 0x00, 4, (u8*)&device_temperature, 0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Device temperature
	{0x2302, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved array
	{0x2302, 0x01, 2, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved sub 1
	{0x2302, 0x02, 2, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved sub 2
	{0x2302, 0x03, 2, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved sub 3
	{0x2302, 0x04, 2, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved sub 4
	
	// I/O Configuration Group (0x6xxx) - Reserved entries
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	{0x6000, 0x00, 4, (u8*)&OD_Num,             0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Reserved
	
	// Motor Control Objects Group (0x6xxx)
	{0x6040, 0x00, 2, (u8*)&control_word.all,   0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Control word
	{0x6041, 0x00, 2, (u8*)&status_word.all,    0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Status word
	{0x6060, 0x00, 2, (u8*)&operation_mode,     0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Operation mode
	{0x6061, 0x00, 2, (u8*)&operation_mode,     0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Operation mode display
	{0x6063, 0x00, 4, (u8*)&pos_actual,         0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Position actual internal
	{0x6064, 0x00, 4, (u8*)&pos_actual,         0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Position actual value
	{0x606C, 0x00, 4, (u8*)&real_speed_filter,  0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Velocity actual value
	{0x6078, 0x00, 4, (u8*)&Iq,                 0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Current actual value
	{0x6079, 0x00, 4, (u8*)&vbus_voltage,       0, 0, 0, 0, 0, 0, fdummy, fdummy},  // DC link circuit voltage
	{0x607A, 0x00, 4, (u8*)&target_position,    0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Target position
	{0x607C, 0x00, 4, (u8*)&home_offest,        0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Home offset
	{0x6081, 0x00, 4, (u8*)&profile_speed,      0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Profile velocity
	{0x6082, 0x00, 4, (u8*)&end_speed,          0, 0, 0, 0, 0, 0, fdummy, fdummy},  // End velocity
	{0x6083, 0x00, 4, (u8*)&profile_acce,       0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Profile acceleration
	{0x6084, 0x00, 4, (u8*)&profile_dece,       0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Profile deceleration
	{0x6098, 0x00, 1, (u8*)&homing_method,      0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Homing method
	{0x6099, 0x00, 4, (u8*)&homing_speed,       0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Homing speeds
	{0x609A, 0x00, 4, (u8*)&homing_acce,        0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Homing acceleration
	{0x60FC, 0x00, 4, (u8*)&position_demand,    0, 0, 0, 0, 0, 0, fdummy, fdummy},  // Position demand value
	{0x60FF, 0x00, 4, (u8*)&target_speed,       0, 0, 0, 0, 0, 0, fdummy, fdummy}   // Target velocity
	
	};

/*---------------------------------------------------------------------*/
 
/* 
  * 功  能：一个无用的函数，用于编译时初始化OD中的对象函数指针。
  * 参  数：无。
  * 返回值：无。
*/				 
void fdummy( void )
{
}

/* 
  * 功  能：在OD中查找对象。
  * 参  数：UINT16 Index,  待查找对象的索引 
            UINT8 SubIndex, 待查找对象的子索引。
  * 返回值：UINT16。找到的对象在数组中的序号。0xFFFF则标识没有找到。
*/
u16 Search_OD( u16 Index, u8 SubIndex )
{
	u16 i, ODLen;
	
	ODLen = sizeof(OD_attribute) / sizeof(DICT_OBJECT);
	
	for( i=0; i<ODLen; i++ )
	{
		if( Index == OD_attribute[i].Index )
		{
			if(SubIndex == OD_attribute[i].SubIndex)
			{
				return i;
			
			}
		}
	}
	
	return 0xFFFF;
}						   
/* 
  * 功  能：在OD中查找对象。
  * 参  数：UINT16 Index,  待查找对象的索引 
            UINT8 SubIndex, 待查找对象的子索引。
  * 返回值：UINT16。找到的对象在数组中的序号。0xFFFF则标识没有找到。
*/
u16 Search_eerom_OD( u8 Flags )
{
	u16 i, ODLen;
	
	ODLen = sizeof(OD_attribute) / sizeof(DICT_OBJECT);
	
	for( i=0; i<ODLen; i++ )
	{
		if( Flags == OD_attribute[i].Flags )
		{		
			return i;
			
		}
	}
	
	return 0xFFFF;
}

u16 Write_OD( DICT_OBJECT Access_OBJ )
{
	u16 Access,Num;
	Num=Search_OD( Access_OBJ.Index, Access_OBJ.SubIndex );
	if(Num==0XFFFF)
	{
		return 0;
	}
	else
	{		
		memcpy(OD_attribute[Num].OD_pointer, Access_OBJ.OD_pointer, OD_attribute[Num].Data_type );
		return 1;
	}	
}	
	 
u16 Write_eerom_OD( DICT_OBJECT Access_OBJ )
{
	u16 Access,Num;
	Num=Search_eerom_OD( Access_OBJ.Flags );
	if(Num==0XFFFF)
	{
		return 0;
	}
	else
	{		
		memcpy(OD_attribute[Num].OD_pointer, Access_OBJ.OD_pointer, OD_attribute[Num].Data_type );
		return 1;
	}	
}

u16 Read_OD( DICT_OBJECT Access_OBJ )
{
	u16 Access,Num;
	Num=Search_OD( Access_OBJ.Index, Access_OBJ.SubIndex );
	if(Num==0XFFFF)
	{
		return 0;
	}
	else
	{		
		memcpy(Access_OBJ.OD_pointer,OD_attribute[Num].OD_pointer,  OD_attribute[Num].Data_type );
		return 1;
	}

	
}

u16 Read_eerom_OD( DICT_OBJECT Access_OBJ )
{
	u16 Access,Num;
	Num=Search_eerom_OD( Access_OBJ.Flags );
	if(Num==0XFFFF)
	{
		return 0;
	}
	else
	{		
		memcpy(Access_OBJ.OD_pointer,OD_attribute[Num].OD_pointer,  OD_attribute[Num].Data_type );
		return 1;
	}

	
}

u8 Cal_sum(u8 *pbuf , u8 num)
{
	u8 i,sum=0;
	for(i=0;i<(num);i++)
	sum+=pbuf[i];
	return sum*(-1);
}

u8 Check_sum(u8 *pbuf)
{
	u8 sum;
	sum=Cal_sum(pbuf,9);
	if(pbuf[9]==sum)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}

u8 RS485_SDO_Process(u8 *pbuf)
{
	DICT_OBJECT SDO_Access_OBJ;
	switch(pbuf[0])
	{
		case SDO_W:
		case SDO_W_1:
		case SDO_W_2:
		case SDO_W_4:
			if(Write_Access)
			{			
				SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
				SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
				SDO_Access_OBJ.OD_pointer=&pbuf[4];	 
				Write_OD(SDO_Access_OBJ); 
//				memcpy(&RS485_TX_BUFF[2],&pbuf[1],7);
//				RS485_TX_BUFF[0]=RS485_Addr; 
//				RS485_TX_BUFF[1]=0x60; 
//				RS485_TX_BUFF[9]=Cal_sum(RS485_TX_BUFF,9);	
//				Modbus_Solve_PutString(RS485_TX_BUFF,10);
				//memcpy(&USART3_TX_Buffer[2],&pbuf[1],7);
				//USART3_TX_Buffer[1]=0x60; 	  
				//USART3_TX_Buffer[9]=Cal_sum(USART3_TX_Buffer,9);	
				//DMA_Config_USART_TX3();	
			}
			else
			{		
				SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
				SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
				SDO_Access_OBJ.OD_pointer=&pbuf[4];	
				switch(SDO_Access_OBJ.Index)
				{
					case 0x8888:	
						Write_OD(SDO_Access_OBJ);
						break; 	
						/* 
					case 0x3206:	
						Write_OD(SDO_Access_OBJ);
						break; 
					case 0x3101:	
						Write_OD(SDO_Access_OBJ);
						break; 
					case 0x3010:	
						Write_OD(SDO_Access_OBJ);
						break; 
						*/
				}  
				//memcpy(&USART3_TX_Buffer[2],&pbuf[1],7);
				//USART3_TX_Buffer[1]=0x60; 	  
				//USART3_TX_Buffer[9]=Cal_sum(USART3_TX_Buffer,9);	
				//DMA_Config_USART_TX3();	
				
			}
			//Save_Parameter();
			break;	
		case SDO_R:		
		case SDO_R_1:		
		case SDO_R_2:		
		case SDO_R_4:	
			SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
			SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
			SDO_Access_OBJ.OD_pointer=&pbuf[4];	 
			Read_OD(SDO_Access_OBJ);
		
//			memcpy(&RS485_TX_BUFF[2],&pbuf[1],7);
//			RS485_TX_BUFF[0]=RS485_Addr; 
//			RS485_TX_BUFF[1]=0x40; 
//			RS485_TX_BUFF[9]=Cal_sum(RS485_TX_BUFF,9);	
//			Modbus_Solve_PutString(RS485_TX_BUFF,10);
//		
			//memcpy(&USART3_TX_Buffer[2],&pbuf[1],7);
			//USART3_TX_Buffer[1]=0x40; 	  
			//USART3_TX_Buffer[9]=Cal_sum(USART3_TX_Buffer,9);	
			//DMA_Config_USART_TX3();
			break;
	}
}

void RS485_SDO_Service(void)
{	
//	if(RS485_FrameFlag==1)
//	{
//		if(Check_sum(RS485_RX_BUFF))
//		{
//			if((RS485_RX_BUFF[0]==RS485_Addr)||(RS485_RX_BUFF[0]==0x7F))
//				RS485_SDO_Process(&RS485_RX_BUFF[1]);
//		}
//		
//		RS485_FrameFlag=0;
//		USART3_RX_STA=0;
//		RS485_RX_CNT=0;
//		RS485_TX_EN=0;
//	}
}
		
u8 Save_Parameter(void)
{	
	DICT_OBJECT SDO_Access_OBJ;	
	u16 i;
								
		//FLASH_Unlock(); 
  	//FLASH_ErasePage(0x08010000);	   
  	//FLASH_ErasePage(0x08010200);
	for(i=1;i<129;i++)	
	{
		SDO_Access_OBJ.Flags=i;	
		SDO_Access_OBJ.OD_pointer=&eerom_pbuf[0];		
		Read_eerom_OD(SDO_Access_OBJ); 			 
		//MemWriteHalfWord(SDO_Access_OBJ.OD_pointer,i*4,4);
	}
}

u8 Read_Parameter(void)
{	
	DICT_OBJECT SDO_Access_OBJ;	
	u16 i;

	for(i=1;i<129;i++)	
	{
		SDO_Access_OBJ.Flags=i;	
		SDO_Access_OBJ.OD_pointer=&eerom_pbuf[0];		 
		//MemReadHalfWord(SDO_Access_OBJ.OD_pointer,i*4,4);			
		Write_eerom_OD(SDO_Access_OBJ); 	
	}
								 
}

u8 Index_To_RSDO(long long index,u8 *pbuf)
{
	pbuf[0]=0x40;
	pbuf[1]=(index>>16)&0xff;
	pbuf[2]=(index>>24)&0xff;
	pbuf[3]=(index>>8)&0xff;
	
}
u8 Index_To_WSDO(long long index,u8 *pbuf,u8 *data)
{
	switch(index&0xff)
	{
		case 0x08:
			pbuf[0]=SDO_W_1;
			pbuf[4]=data[0];
			pbuf[5]=0;
			pbuf[6]=0;
			pbuf[7]=0;
			break;
		case 0x10:
			pbuf[0]=SDO_W_2;
			pbuf[4]=data[0];
			pbuf[5]=data[1];
			pbuf[6]=0;
			pbuf[7]=0;
			break;
		case 0x20:
			pbuf[0]=SDO_W_4;
			pbuf[4]=data[0];
			pbuf[5]=data[1];
			pbuf[6]=data[2];
			pbuf[7]=data[3];
			break;
		default:
			break;
		
	}
	pbuf[1]=(index>>16)&0xff;
	pbuf[2]=(index>>24)&0xff;
	pbuf[3]=(index>>8)&0xff;
	
}
u8 RS232_Read_Index(u8 id,long long index,u8 *pbuf)
{
	pbuf[0]=id;
	Index_To_RSDO(index,(pbuf+1));
	pbuf[9]=Cal_sum(pbuf,9);
}
u8 RS232_Write_Index(u8 id,long long index,u8 *pbuf,u8 *data)
{
	pbuf[0]=id;
	Index_To_WSDO(index,pbuf+1,data);
	pbuf[9]=Cal_sum(pbuf,9);
}

u8 VCP_Process(u8 *pbuf)
{
	DICT_OBJECT SDO_Access_OBJ;
	switch(pbuf[0])
	{
		case SDO_W:
		case SDO_W_1:
		case SDO_W_2:
		case SDO_W_4:
			if(Write_Access)
			{			
				SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
				SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
				SDO_Access_OBJ.OD_pointer=&pbuf[4];	 
				Write_OD(SDO_Access_OBJ); 
				memcpy(&VCP_TX_Buffer[2],&pbuf[1],7);
				VCP_TX_Buffer[0]=RS485_Addr; 
				VCP_TX_Buffer[1]=0x60; 
				VCP_TX_Buffer[9]=Cal_sum(VCP_TX_Buffer,9);	
				//CDC_Transmit_FS(VCP_TX_Buffer, 10);		
			}
			else
			{		
				SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
				SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
				SDO_Access_OBJ.OD_pointer=&pbuf[4];	
				switch(SDO_Access_OBJ.Index)
				{
					case 0x8888:	
						Write_OD(SDO_Access_OBJ);
						break; 	
						/* 
					case 0x3206:	
						Write_OD(SDO_Access_OBJ);
						break; 
					case 0x3101:	
						Write_OD(SDO_Access_OBJ);
						break; 
					case 0x3010:	
						Write_OD(SDO_Access_OBJ);
						break; 
						*/
				}  
				//memcpy(&USART3_TX_Buffer[2],&pbuf[1],7);
				//USART3_TX_Buffer[1]=0x60; 	  
				//USART3_TX_Buffer[9]=Cal_sum(USART3_TX_Buffer,9);	
				//DMA_Config_USART_TX3();	
				
			}
			//Save_Parameter();
			break;	
		case SDO_R:		
		case SDO_R_1:		
		case SDO_R_2:		
		case SDO_R_4:	
			SDO_Access_OBJ.Index=pbuf[1]+pbuf[2]*0x100;
			SDO_Access_OBJ.SubIndex=pbuf[3]; 										 	 
			SDO_Access_OBJ.OD_pointer=&pbuf[4];	 
			Read_OD(SDO_Access_OBJ);
			memcpy(&VCP_TX_Buffer[2],&pbuf[1],7);
			VCP_TX_Buffer[0]=RS485_Addr; 
			VCP_TX_Buffer[1]=0x40; 
			VCP_TX_Buffer[9]=Cal_sum(VCP_TX_Buffer,9);	
			//CDC_Transmit_FS(VCP_TX_Buffer, 10);			
			break;
	}
}


void VCP_Service_Process(void)
{
	if(VCP_RX_Len)
	{
		if(Check_sum(VCP_RX_Buffer))
		{
			if(VCP_RX_Buffer[0]==RS485_Addr)
			{
				VCP_Process(&VCP_RX_Buffer[1]);
				VCP_RX_Buffer[0]=0;
			}
		}
		VCP_RX_Len=0;
	}
			
}



